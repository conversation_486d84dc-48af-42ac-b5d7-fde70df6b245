CMOST_Python/
├── README.md                    # 项目文档
├── setup.py                     # 安装配置
├── requirements.txt             # 依赖管理
├── cmost/                       # 主包
│   ├── __init__.py
│   ├── core/                    # 核心模拟逻辑
│   │   ├── __init__.py
│   │   ├── simulation.py        # 模拟引擎
│   │   ├── progression.py       # 疾病进展模型
│   │   └── population.py        # 人口模型
│   ├── models/                  # 数据模型
│   │   ├── __init__.py
│   │   ├── patient.py           # 患者模型
│   │   ├── polyp.py             # 息肉模型
│   │   └── cancer.py            # 癌症模型
│   ├── utils/                   # 工具函数
│   │   ├── __init__.py
│   │   ├── statistics.py        # 统计工具
│   │   ├── file_io.py           # 文件读写
│   │   └── visualization.py     # 可视化工具
│   ├── calibration/               #调参
│   │   ├── __init__.py
│   │   ├── autocalibration.py        # 自动调参模块
│   │   └── benchmark.py        # 基准值文件
│   ├── config/                  # 配置管理
│   │   ├── __init__.py
│   │   ├── settings.py          # 设置管理
│   │   └── defaults.py          # 默认参数
│   ├── ui/                      # 用户界面
│   │   ├── __init__.py
│   │   ├── main_window.py       # 主窗口
│   │   ├── simulation_panel.py  # 模拟控制面板
│   │   └── results_view.py      # 结果展示
│   └── cluster/                 # 集群计算
│       ├── __init__.py
│       ├── job_manager.py       # 作业管理
│       └── result_collector.py  # 结果收集
├── tests/                       # 测试
│   ├── __init__.py
│   ├── test_simulation.py
│   └── test_models.py
└── examples/                    # 示例
    ├── basic_simulation.py
    └── custom_parameters.py