"""
CMOST (Colorectal Microsimulation Outcomes Screening Tool) v2.0

A comprehensive microsimulation framework for modeling colorectal cancer progression,
screening strategies, and health economic outcomes.

Originally developed by <PERSON> and <PERSON><PERSON> (2012-2016) in MATLAB,
CMOST v2.0 is a complete Python reimplementation with enhanced features, improved
performance, and modern software engineering practices.
"""

__version__ = "2.0.0"
__author__ = "<PERSON>, <PERSON><PERSON>, et al."
__email__ = "<EMAIL>"
__license__ = "GPL-3.0"

# Core simulation components
from .core.simulation import Simulation
from .core.population import PopulationGenerator

# Data models
from .models.patient import Patient
from .models.polyp import Polyp
from .models.cancer import Cancer
from .models.serrated_lesion import SerratedLesion

# Configuration
from .config.settings import Settings, settings, get_setting, set_setting, save_settings, load_settings

# Screening strategies
from .screening.strategy_manager import (
    ScreeningStrategyManager, ScreeningStrategy, ScreeningTest, StrategyType
)

# Machine learning calibration
from .ml.adaptive_calibration import (
    AdaptiveCalibrator, CalibrationData, ParameterSpace, CalibrationTarget
)

# Health economics evaluation
from .economics.health_economics import (
    HealthEconomicsEvaluator, CostData, EconomicOutcome, CostCategory, UtilityState
)

# Utilities
from .utils.statistics import calculate_statistics
from .utils.visualization import plot_results
from .utils.file_io import save_results, load_results

__all__ = [
    # Version info
    '__version__',
    '__author__',
    '__email__',
    '__license__',
    
    # Core classes
    'Simulation',
    'PopulationGenerator',
    
    # Data models
    'Patient',
    'Polyp',
    'Cancer',
    'SerratedLesion',
    
    # Configuration
    'Settings',
    'settings',
    'get_setting',
    'set_setting',
    'save_settings',
    'load_settings',
    
    # Screening strategies
    'ScreeningStrategyManager',
    'ScreeningStrategy',
    'ScreeningTest',
    'StrategyType',

    # Machine learning calibration
    'AdaptiveCalibrator',
    'CalibrationData',
    'ParameterSpace',
    'CalibrationTarget',

    # Health economics evaluation
    'HealthEconomicsEvaluator',
    'CostData',
    'EconomicOutcome',
    'CostCategory',
    'UtilityState',

    # Utilities
    'calculate_statistics',
    'plot_results',
    'save_results',
    'load_results',
]

# Package metadata
__title__ = "CMOST"
__description__ = "Colorectal Microsimulation Outcomes Screening Tool"
__url__ = "https://github.com/misselwitz/cmost"
__version_info__ = tuple(int(i) for i in __version__.split('.'))
