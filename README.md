# CMOST v2.0

**Colorectal Microsimulation Outcomes Screening Tool**

[![License: GPL v3](https://img.shields.io/badge/License-GPLv3-blue.svg)](https://www.gnu.org/licenses/gpl-3.0)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Documentation Status](https://readthedocs.org/projects/cmost/badge/?version=latest)](https://cmost.readthedocs.io/en/latest/?badge=latest)

## Overview

CMOST (Colorectal Microsimulation Outcomes Screening Tool) is a comprehensive microsimulation framework for modeling colorectal cancer progression, screening strategies, and health economic outcomes. Originally developed by <PERSON> and <PERSON><PERSON> (2012-2016) in MATLAB, CMOST v2.0 is a complete Python reimplementation with enhanced features, improved performance, and modern software engineering practices.

The tool simulates the natural history of colorectal polyps and cancer at the individual patient level, allowing researchers and healthcare policy makers to evaluate various screening strategies, estimate cost-effectiveness, and optimize public health interventions.

## Key Features

- **Natural History Modeling**: Detailed simulation of adenoma-carcinoma sequence
- **Screening Strategies**: Evaluate various screening modalities (colonoscopy, FIT, etc.)
- **Health Economics**: Calculate costs, QALYs, and cost-effectiveness metrics
- **Population Modeling**: Simulate diverse populations with different risk profiles
- **Calibration Tools**: Auto-calibration against epidemiological data
- **Visualization**: Comprehensive result visualization and reporting
- **Cluster Computing**: Distribute simulations across computing clusters
- **User Interface**: Both GUI and command-line interfaces

## Installation

### Using pip

```bash
# Basic installation
pip install cmost

# With visualization extras
pip install cmost[visualization]

# With all extras (visualization, cluster computing, calibration, etc.)
pip install cmost[all]
```

### From source

```bash
git clone https://github.com/misselwitz/cmost.git
cd cmost
pip install -e .
```

## Quick Start

### Command Line Interface

```bash
# Run a basic simulation
cmost simulate --patients 10000 --output results.json

# Run a screening strategy comparison
cmost compare --strategies colonoscopy,fit,sigmoidoscopy --output comparison.json

# Calibrate model parameters
cmost calibrate --target-data seer_data.csv --output calibrated_params.json
```

### Python API

```python
from cmost.core.simulation import Simulation
from cmost.config.settings import Settings
from cmost.utils.visualization import plot_results

# Create simulation settings
settings = Settings()
settings.set('Number_patients', 10000)
settings.set('ModelParameters.male_proportion', 0.5)

# Set up and run simulation
sim = Simulation(settings)
results = sim.run(years=50)

# Analyze results
summary = sim.get_summary_statistics()
print(f"Total patients: {summary['total_patients']}")
print(f"Cancer incidence: {summary.get('cancer_incidence', 'N/A')}")
print(f"Cancer mortality: {summary.get('cancer_mortality', 'N/A')}")

# Visualize results
plot_results(results)
```

## Documentation

Comprehensive documentation is available at [https://cmost.readthedocs.io/](https://cmost.readthedocs.io/)

- [User Guide](https://cmost.readthedocs.io/en/latest/user_guide.html)
- [API Reference](https://cmost.readthedocs.io/en/latest/api.html)
- [Examples](https://cmost.readthedocs.io/en/latest/examples.html)
- [Model Description](https://cmost.readthedocs.io/en/latest/model.html)

## Project Structure

```
cmost/
├── core/                    # Core simulation logic
│   ├── simulation.py        # Simulation engine
│   ├── progression.py       # Disease progression model
│   └── population.py        # Population model
├── models/                  # Data models
│   ├── patient.py           # Patient model
│   ├── polyp.py             # Polyp model
│   └── cancer.py            # Cancer model
├── utils/                   # Utility functions
│   ├── statistics.py        # Statistical tools
│   ├── file_io.py           # File I/O
│   └── visualization.py     # Visualization tools
├── calibration/             # Calibration
│   ├── autocalibration.py   # Auto-calibration module
│   └── benchmark.py         # Benchmark values
├── config/                  # Configuration management
│   ├── settings.py          # Settings management
│   └── defaults.py          # Default parameters
├── ui/                      # User interface
│   ├── main_window.py       # Main window
│   ├── simulation_panel.py  # Simulation control panel
│   └── results_view.py      # Results display
└── cluster/                 # Cluster computing
    ├── job_manager.py       # Job management
    └── result_collector.py  # Result collection
```

## Citing CMOST

If you use CMOST in your research, please cite:

```
Misselwitz B, Prakash M, et al. (2023). CMOST: Colorectal Microsimulation Outcomes Screening Tool.
Journal of Medical Screening, XX(X), XXX-XXX. https://doi.org/10.XXXX/XXXXX
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the GNU General Public License v3.0 - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Original MATLAB implementation by Benjamin Misselwitz and Meher Prakash
- SEER Program for providing epidemiological data
- All contributors and testers who have helped improve CMOST

