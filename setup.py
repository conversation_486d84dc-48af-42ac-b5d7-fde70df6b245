#!/usr/bin/env python
# -*- coding: utf-8 -*-

from setuptools import setup, find_packages
import os
import re

# Get package version
with open(os.path.join('cmost', '__init__.py'), 'r') as f:
    version_match = re.search(r"^__version__ = ['\"]([^'\"]*)['\"]", f.read(), re.M)
    version = version_match.group(1) if version_match else '0.1.0'

# Get long description from README
with open('README.md', 'r', encoding='utf-8') as f:
    long_description = f.read()

# Core dependencies
REQUIRED = [
    'numpy>=1.20.0',
    'pandas>=1.3.0',
    'scipy>=1.7.0',
    'matplotlib>=3.4.0',
    'seaborn>=0.11.0',
    'h5py>=3.6.0',
    'pyyaml>=6.0',
    'statsmodels>=0.13.0',
    'scikit-learn>=1.0.0',
    'pyDOE2>=1.3.0',
    'tqdm>=4.62.0',
    'click>=8.0.0',
    'joblib>=1.1.0',
]

# Optional dependencies
EXTRAS = {
    'visualization': [
        'plotly>=5.3.0',
        'kaleido>=0.2.1',
    ],
    'cluster': [
        'paramiko>=2.10.1',
        'dask>=2022.1.0',
        'distributed>=2022.1.0',
    ],
    'calibration': [
        'torch>=1.10.0',
        'tensorboard>=2.8.0',
    ],
    'dev': [
        'pytest>=6.2.5',
        'pytest-cov>=2.12.1',
        'flake8>=4.0.1',
        'black>=22.1.0',
        'mypy>=0.931',
    ],
    'docs': [
        'sphinx>=4.4.0',
        'sphinx-rtd-theme>=1.0.0',
        'nbsphinx>=0.8.8',
        'ipython>=8.0.0',
    ],
    'excel': [
        'openpyxl>=3.0.9',
        'xlrd>=2.0.1',
    ],
    'all': [
        'plotly>=5.3.0',
        'kaleido>=0.2.1',
        'paramiko>=2.10.1',
        'dask>=2022.1.0',
        'distributed>=2022.1.0',
        'torch>=1.10.0',
        'tensorboard>=2.8.0',
        'openpyxl>=3.0.9',
        'xlrd>=2.0.1',
    ]
}

setup(
    name='cmost',
    version=version,
    description='Colorectal Microsimulation Outcomes Screening Tool',
    long_description=long_description,
    long_description_content_type='text/markdown',
    author='Benjamin Misselwitz, Meher Prakash, et al.',
    author_email='<EMAIL>',
    url='https://github.com/misselwitz/cmost',
    packages=find_packages(include=['cmost', 'cmost.*']),
    include_package_data=True,
    install_requires=REQUIRED,
    extras_require=EXTRAS,
    entry_points={
        'console_scripts': [
            'cmost=cmost.cli:main',
        ],
    },
    python_requires='>=3.8',
    classifiers=[
        'Development Status :: 4 - Beta',
        'Intended Audience :: Science/Research',
        'Intended Audience :: Healthcare Industry',
        'License :: OSI Approved :: GNU General Public License v3 (GPLv3)',
        'Natural Language :: English',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Topic :: Scientific/Engineering :: Medical Science Apps.',
        'Topic :: Scientific/Engineering :: Bio-Informatics',
    ],
    keywords=[
        'colorectal cancer',
        'microsimulation',
        'screening',
        'health economics',
        'medical research',
    ],
    project_urls={
        'Documentation': 'https://cmost.readthedocs.io',
        'Source': 'https://github.com/misselwitz/cmost',
        'Issue Tracker': 'https://github.com/misselwitz/cmost/issues',
    },
)