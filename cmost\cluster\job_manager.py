"""
Job management module for CMOST cluster simulations.

This module provides functionality for submitting, monitoring, and managing
simulation jobs on high-performance computing clusters.
"""

import os
import subprocess
import time
import logging
import json
import uuid
from typing import Dict, List, Optional, Tuple, Any, Union
from pathlib import Path
import shutil
import tempfile
import datetime

from ..config.settings import settings
from ..utils.file_io import save_simulation_settings, load_simulation_results


class JobManager:
    """
    Manages simulation jobs on computing clusters.
    
    This class provides an interface for submitting simulation jobs to various
    cluster systems, monitoring job status, and collecting results.
    """
    
    def __init__(self, cluster_type: str = "slurm", base_dir: Optional[str] = None):
        """
        Initialize the job manager.
        
        Args:
            cluster_type: Type of cluster system ('slurm', 'lsf', 'pbs', or 'local')
            base_dir: Base directory for job files (defaults to settings path)
        """
        self.cluster_type = cluster_type.lower()
        self.logger = logging.getLogger("CMOST_JobManager")
        
        # Set up logging if not already configured
        if not self.logger.handlers:
            self.logger.setLevel(logging.INFO)
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
        
        # Set base directory
        if base_dir is None:
            self.base_dir = settings.get("ResultsPath", os.path.join(os.getcwd(), "Results"))
        else:
            self.base_dir = base_dir
            
        # Create directories if they don't exist
        self.jobs_dir = os.path.join(self.base_dir, "Jobs")
        self.results_dir = os.path.join(self.base_dir, "Results")
        self.logs_dir = os.path.join(self.base_dir, "Logs")
        
        for directory in [self.jobs_dir, self.results_dir, self.logs_dir]:
            os.makedirs(directory, exist_ok=True)
        
        # Job tracking
        self.active_jobs: Dict[str, Dict[str, Any]] = {}
        self.completed_jobs: Dict[str, Dict[str, Any]] = {}
        self.failed_jobs: Dict[str, Dict[str, Any]] = {}
        
        # Load existing job status if available
        self._load_job_status()
        
        self.logger.info(f"Job manager initialized with cluster type: {self.cluster_type}")
    
    def _load_job_status(self) -> None:
        """Load existing job status from disk."""
        status_file = os.path.join(self.base_dir, "job_status.json")
        if os.path.exists(status_file):
            try:
                with open(status_file, 'r') as f:
                    status_data = json.load(f)
                    
                self.active_jobs = status_data.get("active_jobs", {})
                self.completed_jobs = status_data.get("completed_jobs", {})
                self.failed_jobs = status_data.get("failed_jobs", {})
                
                self.logger.info(f"Loaded job status: {len(self.active_jobs)} active, "
                                f"{len(self.completed_jobs)} completed, "
                                f"{len(self.failed_jobs)} failed")
            except Exception as e:
                self.logger.error(f"Error loading job status: {str(e)}")
    
    def _save_job_status(self) -> None:
        """Save current job status to disk."""
        status_file = os.path.join(self.base_dir, "job_status.json")
        try:
            status_data = {
                "active_jobs": self.active_jobs,
                "completed_jobs": self.completed_jobs,
                "failed_jobs": self.failed_jobs,
                "last_updated": datetime.datetime.now().isoformat()
            }
            
            with open(status_file, 'w') as f:
                json.dump(status_data, f, indent=2)
        except Exception as e:
            self.logger.error(f"Error saving job status: {str(e)}")
    
    def submit_job(self, 
                  simulation_settings: Dict[str, Any], 
                  job_name: Optional[str] = None,
                  num_patients: Optional[int] = None,
                  time_limit: str = "08:00:00",
                  memory: str = "4G",
                  num_cores: int = 1) -> str:
        """
        Submit a simulation job to the cluster.
        
        Args:
            simulation_settings: Dictionary of simulation settings
            job_name: Name for the job (generated if None)
            num_patients: Override number of patients in simulation
            time_limit: Maximum runtime in HH:MM:SS format
            memory: Memory allocation (e.g., "4G")
            num_cores: Number of CPU cores to request
            
        Returns:
            Job ID string
        """
        # Generate job name and ID if not provided
        if job_name is None:
            job_name = f"cmost_sim_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        job_id = str(uuid.uuid4())
        
        # Override number of patients if specified
        if num_patients is not None:
            simulation_settings["NumberPatients"] = num_patients
        
        # Create job directory
        job_dir = os.path.join(self.jobs_dir, job_id)
        os.makedirs(job_dir, exist_ok=True)
        
        # Save simulation settings to job directory
        settings_file = os.path.join(job_dir, f"{job_name}.json")
        save_simulation_settings(simulation_settings, settings_file)
        
        # Create submission script based on cluster type
        script_path = self._create_submission_script(
            job_id=job_id,
            job_name=job_name,
            settings_file=settings_file,
            time_limit=time_limit,
            memory=memory,
            num_cores=num_cores
        )
        
        # Submit job to cluster
        cluster_job_id = self._submit_to_cluster(script_path)
        
        if cluster_job_id:
            # Record job information
            self.active_jobs[job_id] = {
                "job_id": job_id,
                "cluster_job_id": cluster_job_id,
                "job_name": job_name,
                "settings_file": settings_file,
                "submit_time": datetime.datetime.now().isoformat(),
                "status": "submitted",
                "num_patients": simulation_settings.get("NumberPatients", 0),
                "time_limit": time_limit,
                "memory": memory,
                "num_cores": num_cores
            }
            
            self._save_job_status()
            self.logger.info(f"Job submitted: {job_name} (ID: {job_id}, Cluster ID: {cluster_job_id})")
            return job_id
        else:
            self.logger.error(f"Failed to submit job: {job_name}")
            raise RuntimeError(f"Failed to submit job: {job_name}")
    
    def _create_submission_script(self,
                                 job_id: str,
                                 job_name: str,
                                 settings_file: str,
                                 time_limit: str,
                                 memory: str,
                                 num_cores: int) -> str:
        """
        Create a cluster submission script.
        
        Args:
            job_id: Unique job identifier
            job_name: Name of the job
            settings_file: Path to settings file
            time_limit: Maximum runtime in HH:MM:SS format
            memory: Memory allocation
            num_cores: Number of CPU cores
            
        Returns:
            Path to created script file
        """
        script_path = os.path.join(self.jobs_dir, job_id, f"{job_name}_submit.sh")
        log_file = os.path.join(self.logs_dir, f"{job_name}.log")
        output_file = os.path.join(self.results_dir, f"{job_name}_Results.json")
        
        # Get the path to the Python executable
        python_exec = sys.executable
        
        # Get the path to the main CMOST script
        cmost_script = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                                   "run_simulation.py")
        
        # Create script content based on cluster type
        if self.cluster_type == "slurm":
            script_content = f"""#!/bin/bash
#SBATCH --job-name={job_name}
#SBATCH --output={log_file}
#SBATCH --time={time_limit}
#SBATCH --mem={memory}
#SBATCH --cpus-per-task={num_cores}

echo "Starting CMOST simulation job: {job_name}"
echo "Job ID: {job_id}"
echo "Settings file: {settings_file}"
echo "Output file: {output_file}"
date

{python_exec} {cmost_script} --settings {settings_file} --output {output_file}

exit_code=$?
echo "Job finished with exit code: $exit_code"
date

if [ $exit_code -eq 0 ]; then
    echo "Simulation completed successfully"
    touch {os.path.join(self.jobs_dir, job_id, "COMPLETED")}
else
    echo "Simulation failed"
    touch {os.path.join(self.jobs_dir, job_id, "FAILED")}
fi
"""
        elif self.cluster_type == "lsf":
            script_content = f"""#!/bin/sh
#BSUB -J {job_name}
#BSUB -o {log_file}
#BSUB -W {time_limit.split(':')[0]}
#BSUB -M {memory.replace('G', '000')}
#BSUB -n {num_cores}

echo "Starting CMOST simulation job: {job_name}"
echo "Job ID: {job_id}"
echo "Settings file: {settings_file}"
echo "Output file: {output_file}"
date

{python_exec} {cmost_script} --settings {settings_file} --output {output_file}

exit_code=$?
echo "Job finished with exit code: $exit_code"
date

if [ $exit_code -eq 0 ]; then
    echo "Simulation completed successfully"
    touch {os.path.join(self.jobs_dir, job_id, "COMPLETED")}
else
    echo "Simulation failed"
    touch {os.path.join(self.jobs_dir, job_id, "FAILED")}
fi
"""
        elif self.cluster_type == "pbs":
            script_content = f"""#!/bin/bash
#PBS -N {job_name}
#PBS -o {log_file}
#PBS -l walltime={time_limit}
#PBS -l mem={memory}
#PBS -l ncpus={num_cores}

echo "Starting CMOST simulation job: {job_name}"
echo "Job ID: {job_id}"
echo "Settings file: {settings_file}"
echo "Output file: {output_file}"
date

{python_exec} {cmost_script} --settings {settings_file} --output {output_file}

exit_code=$?
echo "Job finished with exit code: $exit_code"
date

if [ $exit_code -eq 0 ]; then
    echo "Simulation completed successfully"
    touch {os.path.join(self.jobs_dir, job_id, "COMPLETED")}
else
    echo "Simulation failed"
    touch {os.path.join(self.jobs_dir, job_id, "FAILED")}
fi
"""
        else:  # local execution
            script_content = f"""#!/bin/bash
echo "Starting CMOST simulation job: {job_name}"
echo "Job ID: {job_id}"
echo "Settings file: {settings_file}"
echo "Output file: {output_file}"
date

{python_exec} {cmost_script} --settings {settings_file} --output {output_file}

exit_code=$?
echo "Job finished with exit code: $exit_code"
date

if [ $exit_code -eq 0 ]; then
    echo "Simulation completed successfully"
    touch {os.path.join(self.jobs_dir, job_id, "COMPLETED")}
else
    echo "Simulation failed"
    touch {os.path.join(self.jobs_dir, job_id, "FAILED")}
fi
"""
        
        # Write script to file
        with open(script_path, 'w') as f:
            f.write(script_content)
        
        # Make script executable
        os.chmod(script_path, 0o755)
        
        return script_path
    
    def _submit_to_cluster(self, script_path: str) -> Optional[str]:
        """
        Submit job script to cluster.
        
        Args:
            script_path: Path to submission script
            
        Returns:
            Cluster job ID if successful, None otherwise
        """
        try:
            if self.cluster_type == "slurm":
                result = subprocess.run(
                    ["sbatch", script_path],
                    capture_output=True,
                    text=True,
                    check=True
                )
                # Parse job ID from output (e.g., "Submitted batch job 12345")
                job_id = result.stdout.strip().split()[-1]
                return job_id
                
            elif self.cluster_type == "lsf":
                result = subprocess.run(
                    ["bsub", "<", script_path],
                    capture_output=True,
                    text=True,
                    check=True,
                    shell=True
                )
                # Parse job ID from output (e.g., "Job <12345> is submitted")
                job_id = result.stdout.strip().split("<")[1].split(">")[0]
                return job_id
                
            elif self.cluster_type == "pbs":
                result = subprocess.run(
                    ["qsub", script_path],
                    capture_output=True,
                    text=True,
                    check=True
                )
                # Output is just the job ID
                job_id = result.stdout.strip()
                return job_id
                
            else:  # local execution
                # Run in background
                subprocess.Popen(
                    ["bash", script_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                return "local_" + str(uuid.uuid4())[:8]
                
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error submitting job: {str(e)}")
            self.logger.error(f"stdout: {e.stdout}")
            self.logger.error(f"stderr: {e.stderr}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error submitting job: {str(e)}")
            return None
    
    def check_job_status(self, job_id: str) -> str:
        """
        Check the status of a specific job.
        
        Args:
            job_id: Job identifier
            
        Returns:
            Status string ('running', 'completed', 'failed', 'unknown')
        """
        # Check if job is in our tracking dictionaries
        if job_id in self.completed_jobs:
            return "completed"
        elif job_id in self.failed_jobs:
            return "failed"
        elif job_id not in self.active_jobs:
            return "unknown"
        
        # Check for completion/failure markers
        job_dir = os.path.join(self.jobs_dir, job_id)
        if os.path.exists(os.path.join(job_dir, "COMPLETED")):
            # Move job from active to completed
            self.completed_jobs[job_id] = self.active_jobs.pop(job_id)
            self.completed_jobs[job_id]["status"] = "completed"
            self.completed_jobs[job_id]["completion_time"] = datetime.datetime.now().isoformat()
            self._save_job_status()
            return "completed"
            
        if os.path.exists(os.path.join(job_dir, "FAILED")):
            # Move job from active to failed
            self.failed_jobs[job_id] = self.active_jobs.pop(job_id)
            self.failed_jobs[job_id]["status"] = "failed"
            self.failed_jobs[job_id]["failure_time"] = datetime.datetime.now().isoformat()
            self._save_job_status()
            return "failed"
        
        # Check with cluster system
        cluster_job_id = self.active_jobs[job_id]["cluster_job_id"]
        
        try:
            if self.cluster_type == "slurm":
                result = subprocess.run(
                    ["squeue", "-j", cluster_job_id, "-h"],
                    capture_output=True,
                    text=True
                )
                # If job is found in queue, it's still running
                if result.stdout.strip():
                    return "running"
                
                # Check sacct for completed jobs
                result = subprocess.run(
                    ["sacct", "-j", cluster_job_id, "-o", "State", "-n"],
                    capture_output=True,
                    text=True
                )
                state = result.stdout.strip().split()[0] if result.stdout.strip() else ""
                
                if state in ["COMPLETED", "CANCELLED", "FAILED", "TIMEOUT", "OUT_OF_MEMORY"]:
                    status = "completed" if state == "COMPLETED" else "failed"
                    
                    if status == "completed":
                        self.completed_jobs[job_id] = self.active_jobs.pop(job_id)
                        self.completed_jobs[job_id]["status"] = status
                        self.completed_jobs[job_id]["completion_time"] = datetime.datetime.now().isoformat()
                    else:
                        self.failed_jobs[job_id] = self.active_jobs.pop(job_id)
                        self.failed_jobs[job_id]["status"] = status
                        self.failed_jobs[job_id]["failure_time"] = datetime.datetime.now().isoformat()
                        self.failed_jobs[job_id]["failure_reason"] = state
                    
                    self._save_job_status()
                    return status
                
                return "running"
                
            elif self.cluster_type == "lsf":
                result = subprocess.run(
                    ["bjobs", "-noheader", cluster_job_id],
                    capture_output=True,
                    text=True
                )
                # If job is found, check its status
                if result.stdout.strip():
                    status_parts = result.stdout.strip().split()
                    if len(status_parts) >= 3:
                        state = status_parts[2]
                        if state in ["DONE", "EXIT"]:
                            status = "completed" if state == "DONE" else "failed"
                            
                            if status == "completed":
                                self.completed_jobs[job_id] = self.active_jobs.pop(job_id)
                                self.completed_jobs[job_id]["status"] = status
                                self.completed_jobs[job_id]["completion_time"] = datetime.datetime.now().isoformat()
                            else:
                                self.failed_jobs[job_id] = self.active_jobs.pop(job_id)
                                self.failed_jobs[job_id]["status"] = status
                                self.failed_jobs[job_id]["failure_time"] = datetime.datetime.now().isoformat()
                                self.failed_jobs[job_id]["failure_reason"] = state
                            
                            self._save_job_status()
                            return status
                    return "running"
                
                # Job not found in queue, check if it completed
                result = subprocess.run(
                    ["bhist", "-n", "1", "-l", cluster_job_id],
                    capture_output=True,
                    text=True
                )
                if "Done successfully" in result.stdout:
                    self.completed_jobs[job_id] = self.active_jobs.pop(job_id)
                    self.completed_jobs[job_id]["status"] = "completed"
                    self.completed_jobs[job_id]["completion_time"] = datetime.datetime.now().isoformat()
                    self._save_job_status()
                    return "completed"
                elif "Exited" in result.stdout:
                    self.failed_jobs[job_id] = self.active_jobs.pop(job_id)
                    self.failed_jobs[job_id]["status"] = "failed"
                    self.failed_jobs[job_id]["failure_time"] = datetime.datetime.now().isoformat()
                    self._save_job_status()
                    return "failed"
                
                return "unknown"
                
            elif self.cluster_type == "pbs":
                result = subprocess.run(
                    ["qstat", cluster_job_id],
                    capture_output=True,
                    text=True
                )
                # If job is found in queue, it's still running
                if cluster_job_id in result.stdout:
                    return "running"
                
                # Check job completion
                result = subprocess.run(
                    ["qstat", "-x", cluster_job_id],
                    capture_output=True,
                    text=True
                )
                if "Exit_status=0" in result.stdout:
                    self.completed_jobs[job_id] = self.active_jobs.pop(job_id)
                    self.completed_jobs[job_id]["status"] = "completed"
                    self.completed_jobs[job_id]["completion_time"] = datetime.datetime.now().isoformat()
                    self._save_job_status()
                    return "completed"
                elif cluster_job_id in result.stdout:
                    self.failed_jobs[job_id] = self.active_jobs.pop(job_id)
                    self.failed_jobs[job_id]["status"] = "failed"
                    self.failed_jobs[job_id]["failure_time"] = datetime.datetime.now().isoformat()
                    self._save_job_status()
                    return "failed"
                
                return "unknown"
                
            else:  # local execution
                # For local jobs, just check if the process is still running
                # This is a simplified approach
                job_dir = os.path.join(self.jobs_dir, job_id)
                if os.path.exists(os.path.join(job_dir, "COMPLETED")):
                    self.completed_jobs[job_id] = self.active_jobs.pop(job_id)
                    self.completed_jobs[job_id]["status"] = "completed"
                    self.completed_jobs[job_id]["completion_time"] = datetime.datetime.now().isoformat()
                    self._save_job_status()
                    return "completed"
                elif os.path.exists(os.path.join(job_dir, "FAILED")):
                    self.failed_jobs[job_id] = self.active_jobs.pop(job_id)
                    self.failed_jobs[job_id]["status"] = "failed"
                    self.failed_jobs[job_id]["failure_time"] = datetime.datetime.now().isoformat()
                    self._save_job_status()
                    return "failed"
                
                return "running"
                
        except Exception as e:
            self.logger.error(f"Error checking job status: {str(e)}")
            return "unknown"
    
    def update_all_job_statuses(self) -> Dict[str, int]:
        """
        Update status of all active jobs.
        
        Returns:
            Dictionary with counts of jobs in each status
        """
        status_counts = {"running": 0, "completed": 0, "failed": 0, "unknown": 0}
        
        # Make a copy of active_jobs keys to avoid modification during iteration
        active_job_ids = list(self.active_jobs.keys())
        
        for job_id in active_job_ids:
            status = self.check_job_status(job_id)
            status_counts[status] += 1
        
        self.logger.info(f"Job status update: {status_counts}")
        return status_counts
    
    def get_job_results(self, job_id: str) -> Optional[Dict[str, Any]]:
        """
        Get results for a completed job.
        
        Args:
            job_id: Job identifier
            
        Returns:
            Dictionary of results or None if not available
        """
        # Check if job is completed
        status = self.check_job_status(job_id)
        if status != "completed":
            self.logger.warning(f"Cannot get results for job {job_id}, status is {status}")
            return None
        
        # Get job information
        job_info = self.completed_jobs.get(job_id)
        if not job_info:
            self.logger.error(f"Job information not found for job {job_id}")
            return None
        
        # Construct result file path
        job_name = job_info["job_name"]
        result_file = os.path.join(self.results_dir, f"{job_name}_Results.json")
        
        # Check if result file exists
        if not os.path.exists(result_file):
            self.logger.error(f"Result file not found: {result_file}")
            return None
        
        # Load results
        try:
            results = load_simulation_results(result_file)
            return results
        except Exception as e:
            self.logger.error(f"Error loading results for job {job_id}: {str(e)}")
            return None
    
    def cancel_job(self, job_id: str) -> bool:
        """
        Cancel a running job.
        
        Args:
            job_id: Job identifier
            
        Returns:
            True if job was cancelled successfully, False otherwise
        """
        # Check if job is active
        if job_id not in self.active_jobs:
            self.logger.warning(f"Cannot cancel job {job_id}, not in active jobs")
            return False
        
        # Get cluster job ID
        cluster_job_id = self.active_jobs[job_id]["cluster_job_id"]
        
        try:
            if self.cluster_type == "slurm":
                subprocess.run(
                    ["scancel", cluster_job_id],
                    check=True
                )
            elif self.cluster_type == "lsf":
                subprocess.run(
                    ["bkill", cluster_job_id],
                    check=True
                )
            elif self.cluster_type == "pbs":
                subprocess.run(
                    ["qdel", cluster_job_id],
                    check=True
                )
            else:  # local execution
                # For local jobs, try to find and kill the process
                # This is a simplified approach
                job_dir = os.path.join(self.jobs_dir, job_id)
                with open(os.path.join(job_dir, "FAILED"), 'w') as f:
                    f.write("Cancelled by user")
            
            # Move job from active to failed
            self.failed_jobs[job_id] = self.active_jobs.pop(job_id)
            self.failed_jobs[job_id]["status"] = "failed"
            self.failed_jobs[job_id]["failure_time"] = datetime.datetime.now().isoformat()
            self.failed_jobs[job_id]["failure_reason"] = "Cancelled by user"
            self._save_job_status()
            
            self.logger.info(f"Job {job_id} cancelled successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error cancelling job {job_id}: {str(e)}")
            return False
    
    def submit_parameter_sweep(self, 
                              base_settings: Dict[str, Any],
                              parameter_ranges: Dict[str, List[Any]],
                              job_name_prefix: str = "param_sweep",
                              time_limit: str = "08:00:00",
                              memory: str = "4G",
                              num_cores: int = 1) -> List[str]:
        """
        Submit multiple jobs for a parameter sweep.
        
        Args:
            base_settings: Base simulation settings
            parameter_ranges: Dictionary mapping parameter names to lists of values
            job_name_prefix: Prefix for job names
            time_limit: Maximum runtime in HH:MM:SS format
            memory: Memory allocation
            num_cores: Number of CPU cores
            
        Returns:
            List of job IDs
        """
        import itertools
        
        # Get all parameter combinations
        param_names = list(parameter_ranges.keys())
        param_values = list(parameter_ranges.values())
        param_combinations = list(itertools.product(*param_values))
        
        self.logger.info(f"Submitting parameter sweep with {len(param_combinations)} combinations")
        
        # Submit jobs for each parameter combination
        job_ids = []
        for i, combination in enumerate(param_combinations):
            # Create settings for this combination
            settings = base_settings.copy()
            for name, value in zip(param_names, combination):
                settings[name] = value
            
            # Create job name
            job_name = f"{job_name_prefix}_{i+1}"
            
            # Submit job
            try:
                job_id = self.submit_job(
                    simulation_settings=settings,
                    job_name=job_name,
                    time_limit=time_limit,
                    memory=memory,
                    num_cores=num_cores
                )
                job_ids.append(job_id)
            except Exception as e:
                self.logger.error(f"Error submitting job {job_name}: {str(e)}")
        
        self.logger.info(f"Parameter sweep submitted: {len(job_ids)} jobs")
        return job_ids
    
    def get_all_jobs(self) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get information about all jobs.
        
        Returns:
            Dictionary with lists of active, completed, and failed jobs
        """
        return {
            "active": list(self.active_jobs.values()),
            "completed": list(self.completed_jobs.values()),
            "failed": list(self.failed_jobs.values())
        }
    
    def clean_old_jobs(self, days: int = 30) -> int:
        """
        Clean up old job files and records.
        
        Args:
            days: Remove jobs older than this many days
            
        Returns:
            Number of jobs cleaned up
        """
        cutoff_time = datetime.datetime.now() - datetime.timedelta(days=days)
        cutoff_str = cutoff_time.isoformat()
        
        cleaned_count = 0
        
        # Clean completed jobs
        completed_to_remove = []
        for job_id, job_info in self.completed_jobs.items():
            completion_time = job_info.get("completion_time")
            if completion_time and completion_time < cutoff_str:
                # Remove job directory
                job_dir = os.path.join(self.jobs_dir, job_id)
                if os.path.exists(job_dir):
                    try:
                        shutil.rmt