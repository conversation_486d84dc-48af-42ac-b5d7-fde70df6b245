"""
Utility functions for CMOST (Colorectal Microsimulation Outcomes Screening Tool).

This package contains utility functions for statistics, file I/O, and visualization
used throughout the CMOST application.
"""

from .statistics import (
    calculate_statistics,
    calculate_incidence_rates,
    calculate_mortality_rates,
    calculate_screening_metrics,
    calculate_cost_effectiveness
)

from .file_io import (
    save_results,
    load_results,
    export_to_excel,
    import_from_excel,
    save_patient_data,
    load_patient_data
)

from .visualization import (
    plot_results,
    plot_age_distribution,
    plot_incidence_curves,
    plot_survival_curves,
    plot_cost_effectiveness,
    create_summary_report
)

__all__ = [
    # Statistics
    'calculate_statistics',
    'calculate_incidence_rates',
    'calculate_mortality_rates',
    'calculate_screening_metrics',
    'calculate_cost_effectiveness',
    
    # File I/O
    'save_results',
    'load_results',
    'export_to_excel',
    'import_from_excel',
    'save_patient_data',
    'load_patient_data',
    
    # Visualization
    'plot_results',
    'plot_age_distribution',
    'plot_incidence_curves',
    'plot_survival_curves',
    'plot_cost_effectiveness',
    'create_summary_report'
]
