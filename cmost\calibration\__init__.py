"""
Calibration module for CMOST (Colorectal Microsimulation Outcomes Screening Tool).

This package contains functionality for calibrating CMOST model parameters against
epidemiological data, including auto-calibration algorithms and benchmark management.
"""

from .auto_calibration import AutoCalibration, CalibrationResult
from .benchmark import (
    BenchmarkManager,
    create_benchmark_template,
    import_matlab_benchmarks,
    export_to_matlab_benchmarks,
    compare_benchmarks,
    print_benchmark_summary,
    plot_benchmarks
)

__all__ = [
    'AutoCalibration',
    'CalibrationResult',
    'BenchmarkManager',
    'create_benchmark_template',
    'import_matlab_benchmarks',
    'export_to_matlab_benchmarks',
    'compare_benchmarks',
    'print_benchmark_summary',
    'plot_benchmarks'
]
