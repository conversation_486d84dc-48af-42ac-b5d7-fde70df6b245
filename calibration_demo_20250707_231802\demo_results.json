{
  "timestamp": "2025-07-07T23:18:02.874184",
  "best_result": {
    "method": "random_forest",
    "parameters": {
      "early_mult": 5.570341119913534,
      "early_width": 0.9557571693320958,
      "early_center": 70.0451516641515,
      "adv_mult": 3.431552198057149,
      "adv_width": 0.7503984444188009,
      "adv_center": 43.07962155075919,
      "cancer_mult": 0.8636144381099645,
      "cancer_width": 0.4072138916284316,
      "cancer_center": 24.423970413229952
    },
    "error": 21.7,
    "execution_time": 0.7449476718902588,
    "converged": false,
    "validation_error": null
  },
  "all_results": [
    {
      "method": "random_forest",
      "parameters": {
        "early_mult": 5.570341119913534,
        "early_width": 0.9557571693320958,
        "early_center": 70.0451516641515,
        "adv_mult": 3.431552198057149,
        "adv_width": 0.7503984444188009,
        "adv_center": 43.07962155075919,
        "cancer_mult": 0.8636144381099645,
        "cancer_width": 0.4072138916284316,
        "cancer_center": 24.423970413229952
      },
      "error": 21.7,
      "execution_time": 0.7449476718902588,
      "converged": false,
      "validation_error": null
    }
  ],
  "comparison_report": {
    "total_methods_tried": 1,
    "best_method": "random_forest",
    "best_error": 21.7,
    "results": [
      {
        "method": "random_forest",
        "error": 21.7,
        "execution_time": 0.7449476718902588,
        "converged": 